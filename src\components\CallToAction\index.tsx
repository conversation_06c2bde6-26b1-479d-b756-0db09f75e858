import React from "react";

const CallToActionSection: React.FC = () => {
  return (
    <section className="bg-primary pt-[100px] pb-20">
      <div className="mx-auto">
        <div className="flex flex-col lg:flex-row items-center justify-end gap-8">
          {/* Left Content */}
          <div className="text-white flex w-full justify-center ">
            <h2 className="text-[64px] leading-[1.1]">
              Ready to Open Your <br />
              Digital Dollar Account?
            </h2>
          </div>

          {/* Right Content - Call to Action Card */}
          <div className="max-w-[50%] w-full h-[370px] flex items-center bg-white rounded-l-3xl pl-[60px]">
            <div className="text-[40px] text-gray-800">
              <p>
                Dial <span className="font-bold">*334#</span> or{" "}
                <span className="text-[#1B5E4B] font-semibold underline cursor-pointer">
                  download
                </span>{" "}
                the Zybra
              </p>
              <p>app today. Start saving in real U.S.</p>
              <p>dollars now.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToActionSection;
