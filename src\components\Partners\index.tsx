"use client";

import React, { useEffect, useRef } from "react";

const TrustedByCarousel = () => {
  const carouselRef = useRef<HTMLDivElement>(null);

  const companies = [
    { name: "Yellow Card", logo: "/yellow_card_logo.png" },
    { name: "Kotani Pay", logo: "/kotani_pay_logo.png" },
    { name: "Chainlink", logo: "/chainlink_logo.png" },
    { name: "Swarm", logo: "/swarm_logo.png" },
    { name: "Centrifuge", logo: "/centrifuge_logo.png" },
  ];

  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    let animationId: number | undefined;

    interface Company {
      name: string;
      logo: string;
    }
    let scrollPosition = 0;
    const scrollSpeed = 0.5;

    const animate = () => {
      scrollPosition += scrollSpeed;

      if (scrollPosition >= carousel.scrollWidth / 2) {
        scrollPosition = 0;
      }

      carousel.scrollLeft = scrollPosition;
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    const handleMouseEnter = () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };

    const handleMouseLeave = () => {
      animationId = requestAnimationFrame(animate);
    };

    carousel.addEventListener("mouseenter", handleMouseEnter);
    carousel.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      carousel.removeEventListener("mouseenter", handleMouseEnter);
      carousel.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, []);

  return (
    <section className="bg-primary py-12 overflow-hidden pt-52 pb-40">
      <div className="flex gap-12 max-w-[1500px] mx-auto px-4">
        <div className="flex items-center text-white gap-3 flex-shrink-0">
          <svg
            width="auto"
            height="auto"
            viewBox="0 0 38 39"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-10"
          >
            <path
              d="M6.15039 9.40763L18.9205 5.30859V33.6866C9.79854 29.9028 6.15039 22.6507 6.15039 18.5516V9.40763ZM31.6906 9.40763L18.9205 5.30859V33.6866C28.0424 29.9028 31.6906 22.6507 31.6906 18.5516V9.40763Z"
              fill="#EAEAEA"
            />
          </svg>
          <span className="text-[32px] whitespace-nowrap">Trusted by</span>
        </div>

        <div className="relative overflow-hidden flex-1 border-l-[0.32px] border-r-[0.32px] border-[#EAEAEA]">
          <div
            ref={carouselRef}
            className="flex overflow-hidden"
            style={{ scrollBehavior: "unset" }}
          >
            {companies.map((company, index) => (
              <div
                key={`first-${index}`}
                className="flex items-center justify-center min-w-[200px] px-10 flex-shrink-0"
              >
                <img
                  src={company.logo}
                  alt={company.name}
                  className="max-h-16 w-auto opacity-90 hover:opacity-100 transition-opacity duration-300"
                />
              </div>
            ))}
            {companies.map((company, index) => (
              <div
                key={`second-${index}`}
                className="flex items-center justify-center min-w-[200px] px-10 flex-shrink-0"
              >
                <img
                  src={company.logo}
                  alt={company.name}
                  className="max-h-16 w-auto opacity-90 hover:opacity-100 transition-opacity duration-300"
                />
              </div>
            ))}
          </div>

          <div className="absolute top-0 left-0 w-8 h-full pointer-events-none z-10"></div>
          <div className="absolute top-0 right-0 w-8 h-full pointer-events-none z-10"></div>
        </div>
      </div>
    </section>
  );
};

export default TrustedByCarousel;
