"use client";

import React, { useState, useRef } from "react";
import {
  Splide,
  SplideSlide,
  Splide as SplideClass,
} from "@splidejs/react-splide";
import "@splidejs/react-splide/css";
import { AnimatePresence, motion } from "framer-motion";
import Mockup from "/iphone-hand-mockup.png";

const HowItWorksSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const splideRef = useRef<SplideClass | null>(null);

  const slides = [
    {
      title: "Connect Your Mobile Money",
      description:
        "Use your existing mobile number or mobile money account. No new sign-ups.",
      step: "01/04",
      image: "/iphone-hand-mockup.png",
    },
    {
      title: "Open Dollar Account",
      description:
        "Create your virtual dollar savings account instantly with just a few taps.",
      step: "02/04",
      image: "/iphone-hand-mockup.png",
    },
    {
      title: "Save & Grow",
      description:
        "Transfer money anytime and watch your savings grow up to 10% annually.",
      step: "03/04",
      image: "/iphone-hand-mockup.png",
    },
    {
      title: "Access Anytime",
      description:
        "Withdraw or transfer your dollars back to mobile money whenever you need.",
      step: "04/04",
      image: "/iphone-hand-mockup.png",
    },
  ];

  return (
    <div className="bg-gray-50 py-12 sm:py-16 md:py-20">
      <div className="px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 my-8 sm:my-12 md:my-16">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-10 md:mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-[64px] text-[#021018] font-semibold mb-2 sm:mb-0 leading-tight">
            How Zybra Works
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-[36px] text-[#021018] leading-tight">
            Your Virtual Dollar Account in 4 Easy Steps
          </p>
        </div>

        {/* Slider Container */}
        <div className="max-w-6xl mx-auto">
          {/* Image Slider */}
          <Splide
            ref={splideRef}
            onMove={(splide) => setCurrentSlide(splide.index)}
            options={{
              type: "loop",
              perPage: 1,
              autoplay: true,
              interval: 4000,
              pauseOnHover: false,
              pauseOnFocus: false,
              speed: 800,
              arrows: false,
              pagination: false,
              drag: true,
            }}
          >
            {slides.map((slide, index) => (
              <SplideSlide key={index}>
                <div className="bg-[#104E47] rounded-2xl sm:rounded-3xl min-h-[280px] sm:min-h-[350px] md:min-h-[380px] lg:min-h-[420px] xl:min-h-[562px] flex items-end justify-center p-4 sm:p-6 md:p-8">
                  <img
                    src={slide.image}
                    alt=""
                    className="max-h-full w-auto object-contain"
                  />
                </div>
              </SplideSlide>
            ))}
          </Splide>

          {/* Text Content with Animation */}
          <div className="mt-4 sm:mt-6 md:mt-8 relative min-h-[100px] sm:min-h-[120px] md:min-h-[120px] lg:min-h-[120px] xl:min-h-[140px]">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
                className="absolute inset-0"
              >
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                  <div className="flex-1">
                    <h3 className="text-2xl sm:text-3xl md:text-3xl lg:text-4xl xl:text-[48px] text-[#0D2E42] font-semibold mb-2 sm:mb-3 leading-tight">
                      {slides[currentSlide].title}
                    </h3>
                    <p className="text-base sm:text-lg md:text-lg lg:text-xl xl:text-[42px] text-[#0D2E42] leading-relaxed xl:leading-[1.2] font-light max-w-none sm:max-w-3xl">
                      {slides[currentSlide].description}
                    </p>
                  </div>
                  <span className="text-xl sm:text-2xl md:text-2xl lg:text-2xl xl:text-[32px] text-[#0D2E42] font-semibold self-start sm:self-auto flex-shrink-0">
                    {slides[currentSlide].step}
                  </span>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center gap-2 sm:gap-3 mt-16 sm:mt-20 md:mt-24 lg:mt-28 xl:mt-36">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  splideRef.current?.go(index);
                }}
                className={`transition-all duration-300 ${
                  index === currentSlide
                    ? "w-8 sm:w-10 md:w-12 h-1.5 sm:h-2 bg-teal-500 rounded-full"
                    : "w-1.5 sm:w-2 h-1.5 sm:h-2 bg-gray-300 rounded-full"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorksSlider;
