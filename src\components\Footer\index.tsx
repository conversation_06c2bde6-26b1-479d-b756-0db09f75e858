import React from "react";
import { Icon } from "@iconify/react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-primary">
      <div className="max-w-[1500px] flex flex-col md:flex-row justify-between mx-auto pt-20 pb-[120px] px-4">
        {/* Logo Section */}
        <div className="md:col-span-1">
          <div className="flex items-center mb-4">
            <div className="w-full max-w-[50px] mr-3">
              <img src="/zybra_logo_white.png" alt="Zybra Logo White" />
            </div>
            <span className="text-white text-2xl">Zybra</span>
          </div>
        </div>

        <div className="text-[26px] flex flex-col md:flex-row gap-20">
          {/* About Zybra */}
          <div className="md:col-span-1">
            <ul className="space-y-4">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors"
                >
                  About Zybra
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors"
                >
                  Terms & Conditions
                </a>
              </li>
            </ul>
          </div>

          {/* Privacy Policy */}
          <div className="md:col-span-1">
            <ul className="space-y-4">
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 hover:text-white transition-colors"
                >
                  Contact Us
                </a>
              </li>
            </ul>
          </div>

          {/* Social */}
          <div className="md:col-span-1">
            <h3 className="text-white/80 mb-4">Socials</h3>
            <div className="flex space-x-4">
              {/* Social Media Icons */}
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:twitter-circle-filled"
                  className="w-[26px] h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:linkedin-filled"
                  className="w-[26px] h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ant-design:github-filled"
                  className="w-[26px] h-[26px]"
                />
              </a>
              <a
                href="#"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Icon
                  icon="ic:baseline-discord"
                  className="w-[26px] h-[26px]"
                />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Zybra Pattern/Logo */}
      <div className="w-full max-w-[800px]">
        <img src="/zybra.png" alt="Zybra" />
      </div>
    </footer>
  );
};

export default Footer;
