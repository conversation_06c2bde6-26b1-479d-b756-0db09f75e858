"use client";

import React from "react";

const ZybraLanding = () => {
  return (
    <div className="bg-primary p-4 md:p-6 flex flex-col items-center min-h-screen">
      {/* Header */}
      <div className="text-center mb-8 md:mb-12 pt-4 md:pt-8 px-4">
        <h1 className="text-3xl md:text-5xl lg:text-6xl xl:text-[64px] font-bold text-white leading-tight">
          Why choose Zybra
        </h1>
        <p className="text-lg md:text-2xl lg:text-3xl xl:text-[36px] text-gray-200 mt-2 md:mt-4">
          Trusted digital Dollar account for Africa.
        </p>
      </div>

      {/* Main Container - Responsive Grid */}
      <div className="w-full max-w-[1500px] px-4">
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-4 md:gap-6">
          {/* Left Column */}
          <div className="xl:col-span-7 flex flex-col gap-4 md:gap-5">
            {/* First Row - Two cards side by side on larger screens, stacked on mobile */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5">
              {/* Save in Real U.S. Dollars Card */}
              <div className="bg-[#F8F9FA] rounded-2xl p-4 md:p-5 lg:p-6 flex flex-col justify-between shadow-lg min-h-[200px] md:min-h-[250px] lg:min-h-[300px] xl:min-h-[378px]">
                <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-tight font-normal text-[#0F3D3E]">
                  Save in Real
                  <br />
                  U.S. Dollars
                </h2>
                <p className="text-[#0F3D3E] text-lg md:text-xl lg:text-2xl xl:text-[32px] leading-tight mt-4 md:mt-0">
                  Protect your savings <br className="hidden md:block" /> from
                  inflation
                </p>
              </div>

              {/* Always Accessible Card */}
              <div className="bg-[#F5E6CA] rounded-2xl p-4 md:p-5 lg:p-6 flex flex-col justify-between shadow-lg min-h-[200px] md:min-h-[250px] lg:min-h-[300px] xl:min-h-[378px]">
                <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-tight font-medium text-[#0F3D3E]">
                  Always
                  <br />
                  Accessible
                </h2>
                <p className="text-[#0F3D3E] text-lg md:text-xl lg:text-2xl xl:text-[32px] leading-tight mt-4 md:mt-0">
                  USSD, mobile app,
                  <br />
                  and web access.
                </p>
              </div>
            </div>

            {/* Logo Card - Full width */}
            <div className="bg-[#1C1C1C] rounded-2xl p-8 md:p-12 lg:p-16 xl:p-24 flex items-center justify-center shadow-lg min-h-[200px] md:min-h-[250px] lg:min-h-[300px] xl:h-[385px]">
              <div className="text-white text-xl md:text-2xl lg:text-3xl">
                <svg
                  width="100%"
                  height="100%"
                  viewBox="0 0 595 159"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M56.8849 3.5078C58.9598 5.43517 64.0884 10.6776 68.2775 15.1491C77.9084 25.5569 84.9945 32.187 95.2518 40.3976C114.083 55.5081 138.238 65.1064 171.124 70.503C174.883 71.1198 179.189 71.7751 180.716 71.8907L183.535 72.1606L183.77 73.9337C184.083 76.208 184.083 88.6202 183.731 90.2007L183.496 91.4727L179.15 91.2029C133.149 88.4661 106.292 80.7181 84.2115 63.8729C73.2104 55.4696 66.0851 48.6467 55.9452 36.7356C49.094 28.6792 39.5806 18.8882 34.6868 14.8022L32.377 12.8748L35.4698 10.4078C40.6768 6.2832 50.8949 0.0385437 52.4609 0C52.7741 0 54.7708 1.58043 56.8849 3.5078ZM17.6958 35.2708C25.7215 37.9691 36.0179 43.6355 41.4989 48.3769C42.2428 48.9936 46.9799 53.6193 52.0694 58.6304C57.1197 63.603 63.188 69.1924 65.4978 71.0041C81.3144 83.3393 97.3267 90.6633 115.297 93.6699C122.383 94.8649 130.957 95.2118 158.087 95.4817L183.222 95.713L182.987 97.1007C182.478 99.9146 181.734 103.345 181.617 103.461C181.538 103.5 178.915 104.887 175.783 106.468C164.038 112.481 152.293 119.805 140.353 128.594C127.316 138.231 118.311 144.321 111.107 148.523C98.2663 155.924 84.603 159.933 75.7943 158.815C63.5403 157.273 50.8166 150.566 34.2562 136.881C27.9139 131.639 23.4508 127.514 12.2539 116.644L3.95414 108.588L2.89709 104.193C2.30984 101.803 1.95749 99.7219 2.11409 99.5677C2.50559 99.1822 6.14653 100.994 10.1398 103.654C15.1119 106.93 25.056 114.563 38.1712 125.24C56.5325 140.197 64.4016 143.974 76.7339 143.936C86.9911 143.936 96.9352 140.89 112.556 133.027C120.229 129.172 135.576 120.653 135.341 120.422C135.263 120.345 133.345 121.116 131.113 122.156C120.386 127.129 108.524 131.099 99.2451 132.834C94.1947 133.759 86.6388 133.682 81.5885 132.641C69.726 130.213 59.7036 125.009 42.6734 112.442C37.3882 108.549 37.4665 108.626 28.3837 99.8761C18.5179 90.3549 11.5884 85.4594 3.24944 82.0672L0.0391478 80.7566L0 77.4801C0 75.6684 0.117449 73.2784 0.234899 72.1991C0.548098 69.8477 0.0391489 69.8863 6.06824 71.698C11.1186 73.2399 16.0123 75.2443 19.6924 77.3259C22.7853 79.0605 28.3054 82.8382 29.9497 84.3029C30.3803 84.6884 32.3378 86.3845 34.2562 88.0806C36.2137 89.7767 38.6801 91.9739 39.7372 92.9375C50.5817 102.883 65.3804 112.211 75.7551 115.603C90.3972 120.383 104.844 120.152 129.939 114.64C145.677 111.209 169.911 104.887 170.889 104.001C171.124 103.769 170.929 103.731 170.302 103.846C168.071 104.386 154.368 106.429 148.3 107.162C137.221 108.511 128.255 109.089 118.233 109.089C100.498 109.089 90.867 107.123 77.3211 100.647C65.028 94.7878 59.9777 90.7404 39.3457 70.3103C28.4229 59.5171 26.2696 57.5897 22.5895 55.1612C18.3222 52.3858 13.1935 49.8031 8.76958 48.2227L6.34229 47.3361L7.51678 44.792C8.57383 42.4791 13.311 33.9216 13.5459 33.9216C13.585 33.9216 15.4642 34.5384 17.6958 35.2708Z"
                    fill="white"
                  />
                  <path
                    d="M241.455 126V115.5L289.182 47.0455H241.182V32.9091H310.091V43.4091L262.409 111.864H310.364V126H241.455ZM336.182 152.182C333.939 152.182 331.864 152 329.955 151.636C328.076 151.303 326.576 150.909 325.455 150.455L329.273 137.636C331.667 138.333 333.803 138.667 335.682 138.636C337.561 138.606 339.212 138.015 340.636 136.864C342.091 135.742 343.318 133.864 344.318 131.227L345.727 127.455L320.409 56.1818H337.864L353.955 108.909H354.682L370.818 56.1818H388.318L360.364 134.455C359.061 138.152 357.333 141.318 355.182 143.955C353.03 146.621 350.394 148.652 347.273 150.045C344.182 151.47 340.485 152.182 336.182 152.182ZM400.432 126V32.9091H416.886V67.7273H417.568C418.417 66.0303 419.614 64.2273 421.159 62.3182C422.705 60.3788 424.795 58.7273 427.432 57.3636C430.068 55.9697 433.432 55.2727 437.523 55.2727C442.917 55.2727 447.78 56.6515 452.114 59.4091C456.477 62.1364 459.932 66.1818 462.477 71.5455C465.053 76.8788 466.341 83.4242 466.341 91.1818C466.341 98.8485 465.083 105.364 462.568 110.727C460.053 116.091 456.629 120.182 452.295 123C447.962 125.818 443.053 127.227 437.568 127.227C433.568 127.227 430.25 126.561 427.614 125.227C424.977 123.894 422.856 122.288 421.25 120.409C419.674 118.5 418.447 116.697 417.568 115H416.614V126H400.432ZM416.568 91.0909C416.568 95.6061 417.205 99.5606 418.477 102.955C419.78 106.348 421.644 109 424.068 110.909C426.523 112.788 429.492 113.727 432.977 113.727C436.614 113.727 439.659 112.758 442.114 110.818C444.568 108.848 446.417 106.167 447.659 102.773C448.932 99.3485 449.568 95.4545 449.568 91.0909C449.568 86.7576 448.947 82.9091 447.705 79.5455C446.462 76.1818 444.614 73.5455 442.159 71.6364C439.705 69.7273 436.644 68.7727 432.977 68.7727C429.462 68.7727 426.477 69.697 424.023 71.5455C421.568 73.3939 419.705 75.9848 418.432 79.3182C417.189 82.6515 416.568 86.5758 416.568 91.0909ZM480.33 126V56.1818H496.284V67.8182H497.011C498.284 63.7879 500.466 60.6818 503.557 58.5C506.678 56.2879 510.239 55.1818 514.239 55.1818C515.148 55.1818 516.163 55.2273 517.284 55.3182C518.436 55.3788 519.39 55.4848 520.148 55.6364V70.7727C519.451 70.5303 518.345 70.3182 516.83 70.1364C515.345 69.9242 513.905 69.8182 512.511 69.8182C509.511 69.8182 506.814 70.4697 504.42 71.7727C502.057 73.0455 500.193 74.8182 498.83 77.0909C497.466 79.3636 496.784 81.9848 496.784 84.9545V126H480.33ZM549.92 127.409C545.496 127.409 541.511 126.621 537.966 125.045C534.451 123.439 531.663 121.076 529.602 117.955C527.572 114.833 526.557 110.985 526.557 106.409C526.557 102.47 527.284 99.2121 528.739 96.6364C530.193 94.0606 532.178 92 534.693 90.4545C537.208 88.9091 540.042 87.7424 543.193 86.9545C546.375 86.1364 549.663 85.5455 553.057 85.1818C557.148 84.7576 560.466 84.3788 563.011 84.0455C565.557 83.6818 567.405 83.1364 568.557 82.4091C569.739 81.6515 570.33 80.4848 570.33 78.9091V78.6364C570.33 75.2121 569.314 72.5606 567.284 70.6818C565.254 68.803 562.33 67.8636 558.511 67.8636C554.481 67.8636 551.284 68.7424 548.92 70.5C546.587 72.2576 545.011 74.3333 544.193 76.7273L528.83 74.5455C530.042 70.303 532.042 66.7576 534.83 63.9091C537.617 61.0303 541.027 58.8788 545.057 57.4545C549.087 56 553.542 55.2727 558.42 55.2727C561.784 55.2727 565.133 55.6667 568.466 56.4545C571.799 57.2424 574.845 58.5455 577.602 60.3636C580.36 62.1515 582.572 64.5909 584.239 67.6818C585.936 70.7727 586.784 74.6364 586.784 79.2727V126H570.966V116.409H570.42C569.42 118.348 568.011 120.167 566.193 121.864C564.405 123.53 562.148 124.879 559.42 125.909C556.723 126.909 553.557 127.409 549.92 127.409ZM554.193 115.318C557.496 115.318 560.36 114.667 562.784 113.364C565.208 112.03 567.072 110.273 568.375 108.091C569.708 105.909 570.375 103.53 570.375 100.955V92.7273C569.86 93.1515 568.981 93.5455 567.739 93.9091C566.527 94.2727 565.163 94.5909 563.648 94.8636C562.133 95.1364 560.633 95.3788 559.148 95.5909C557.663 95.803 556.375 95.9848 555.284 96.1364C552.83 96.4697 550.633 97.0152 548.693 97.7727C546.754 98.5303 545.223 99.5909 544.102 100.955C542.981 102.288 542.42 104.015 542.42 106.136C542.42 109.167 543.527 111.455 545.739 113C547.951 114.545 550.769 115.318 554.193 115.318Z"
                    fill="white"
                  />
                </svg>
              </div>
            </div>

            {/* Safe & Transparent Card - Full width */}
            <div className="flex flex-col justify-between bg-[#EADBC8] rounded-2xl p-4 md:p-6 shadow-lg min-h-[280px] md:min-h-[350px] lg:min-h-[400px] xl:h-[515px]">
              <div>
                <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-tight font-medium text-[#0F3D3E]">
                  Safe & Transparent
                </h2>
                <p className="text-[#0F3D3E] text-lg md:text-xl lg:text-2xl xl:text-[32px] leading-tight mt-3 md:mt-5">
                  Powered by global financial markets
                  <br className="hidden sm:block" />
                  and secure technology.
                </p>
              </div>
              <button className="bg-[#1C1C1C] w-fit text-white px-6 md:px-8 py-3 md:py-4 rounded-xl text-lg md:text-xl lg:text-2xl xl:text-[30px] font-medium hover:bg-gray-800 transition-colors mt-6 md:mt-0">
                Start earning now
              </button>
            </div>
          </div>

          {/* Right Column */}
          <div className="xl:col-span-5 flex flex-col gap-4 md:gap-5">
            {/* No Bank Account Card with Phone */}
            <div className="bg-[#D4AF37] rounded-2xl p-4 md:p-6 shadow-lg relative overflow-hidden min-h-[320px] md:min-h-[400px] lg:min-h-[500px] xl:h-[650px]">
              <div className="relative z-10">
                <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-tight font-medium text-[#0F3D3E]">
                  No Bank Account
                  <br />
                  Needed
                </h2>
                <p className="text-[#0F3D3E] text-lg md:text-xl lg:text-2xl xl:text-[32px] leading-tight mt-3 md:mt-5">
                  Skip paperwork, queues,
                  <br />
                  and minimum deposits
                </p>
              </div>

              {/* Phone Mockup - Responsive positioning and sizing */}
              <div className="absolute bottom-0 right-2 md:right-4 w-32 md:w-40 lg:w-48 h-40 md:h-52 lg:h-64">
                <div className="bg-gray-900 rounded-xl md:rounded-2xl p-2 md:p-3 shadow-2xl transform rotate-6 h-full">
                  <div className="bg-blue-900 rounded-xl md:rounded-2xl h-full p-3 md:p-4">
                    <div className="text-white text-xs mb-2">Zybra</div>
                    <div className="bg-blue-800 rounded-lg p-2 md:p-3 mb-2">
                      <div className="text-blue-300 text-xs">
                        Enter your phone number
                      </div>
                    </div>
                    <div className="flex gap-1 md:gap-2 mb-2">
                      <div className="bg-blue-700 h-6 md:h-8 w-8 md:w-12 rounded"></div>
                      <div className="bg-blue-700 h-6 md:h-8 w-8 md:w-12 rounded"></div>
                      <div className="bg-blue-700 h-6 md:h-8 w-8 md:w-12 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Instant Withdrawals Card */}
            <div className="flex flex-col justify-between bg-[#FAF3E0] rounded-2xl p-4 md:p-6 shadow-lg min-h-[200px] md:min-h-[225px] lg:min-h-[250px] xl:h-[290px]">
              <h2 className="text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-tight font-medium text-[#0F3D3E]">
                Instant
                <br />
                Withdrawals
              </h2>
              <p className="text-[#0F3D3E] text-lg md:text-xl lg:text-2xl xl:text-[32px] leading-tight mt-3 md:mt-5">
                Access your virtual
                <br />
                dollar account anytime.
              </p>
            </div>

            {/* Dashboard Preview Card */}
            <div className="bg-gray-900 rounded-2xl p-4 md:p-6 shadow-lg relative overflow-hidden min-h-[200px] md:min-h-[255px] xl:h-[338px] flex items-center justify-center">
              {/* Dashboard Mockup Placeholder */}
              <div className="absolute inset-0 flex items-center justify-center">
                <img src="/bento_mobile_card.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZybraLanding;
