declare module "@splidejs/react-splide" {
  import * as React from "react";

  export interface SplideProps {
    options?: any;
    onMove?: (splide: any) => void;
    [key: string]: any;
  }

  export class Splide extends React.Component<SplideProps> {
      go(index: number) {
          throw new Error("Method not implemented.");
      }
}
  export class SplideSlide extends React.Component<{
    children?: React.ReactNode;
  }> {}
}
