"use client";

import React, { useState } from "react";

interface FAQItem {
  id: number;
  question: string;
  answer?: string;
}

const FAQSection: React.FC = () => {
  const [openItem, setOpenItem] = useState<number | null>(2);

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "Is Zybra a bank?",
      answer:
        "No, Zybra is not crypto. We use secure global distributed financial technology to give you a digital dollar account — a simple, virtual way to save and grow real U.S. dollars.",
    },
    {
      id: 2,
      question: "Is this a crypto app?",
      answer:
        "No, Zybra is not crypto. We use secure global distributed financial technology to give you a digital dollar account — a simple, virtual way to save and grow real U.S. dollars.",
    },
    {
      id: 3,
      question: "Where is my money allocated?",
      answer:
        "Your money is allocated in secure, regulated financial institutions that provide the infrastructure for your digital dollar account.",
    },
    {
      id: 4,
      question: "Can I lose money?",
      answer:
        "While we take extensive measures to protect your funds, no investment is without risk. It's important to understand the risks involved in any financial product.",
    },
    {
      id: 5,
      question: "How do I start?",
      answer:
        "Getting started with <PERSON>y<PERSON> is easy! Simply sign up for an account, complete the verification process, and you can begin saving and growing your digital dollars.",
    },
  ];

  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <section className="bg-white py-28 px-4">
      <div className="max-w-[1300px] mx-auto">
        {/* Header */}
        <h2 className="text-[64px] font-bold text-[#021018] text-center mb-12">
          Frequently asked questions
        </h2>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqItems.map((item) => (
            <div
              key={item.id}
              className={`border border-[#E7EBEE] rounded-2xl overflow-hidden ${
                openItem === item.id ? "bg-white" : "bg-[#FAFAFA]"
              }`}
            >
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full flex items-start gap-10 px-24 py-20 text-left focus:outline-none"
                aria-expanded={openItem === item.id}
              >
                {/* Icon: fixed container + tight line-height */}
                <span className="flex-shrink-0 leading-none">
                  {openItem === item.id ? (
                    <span className="text-[48px] text-red-400 font-light leading-none">
                      −
                    </span>
                  ) : (
                    <span className="text-[48px] text-gray-600 font-light leading-none">
                      +
                    </span>
                  )}
                </span>

                {/* Question + Answer stacked */}
                <div className="flex flex-col">
                  <span className="text-[32px] font-semibold text-[#021018]">
                    {item.question}
                  </span>
                  {openItem === item.id && item.answer && (
                    <span className="mt-4 text-[32px] font-light text-[#021018] leading-[1.2] max-w-[750px]">
                      {item.answer}
                    </span>
                  )}
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
