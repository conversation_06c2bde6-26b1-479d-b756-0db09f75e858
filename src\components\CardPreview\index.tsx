import React from "react";

const CardPreviewSection: React.FC = () => {
  return (
    <section className="bg-gray-50 py-[300px] px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* Card Container */}
        {/* <div className="mb-12">
          <div className="inline-block perspective-1000">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 w-80 h-48 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <div className="w-12 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md mb-6"></div>
              <div className="text-white font-mono text-lg tracking-wider mb-4">
                0000 0000 0000 0001
              </div>
              <div className="flex justify-between items-end text-white">
                <div>
                  <div className="text-xs text-gray-400 mb-1">01/23</div>
                  <div className="text-sm font-medium">CARD HOLDER</div>
                </div>
              </div>
            </div>
          </div>
        </div> */}

        {/* Card Image */}
        <div className="w-full max-w-xl mx-auto">
          <img src="/card.png" alt="Card" />
        </div>

        {/* Text Content */}
        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          3D for this in the works
        </h2>
      </div>
    </section>
  );
};

export default CardPreviewSection;
